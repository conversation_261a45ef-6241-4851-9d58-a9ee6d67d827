-- SoloShop Database Export
-- Generated on: 2025-07-13 19:16:00

SET FOREIGN_KEY_CHECKS = 0;

-- Table structure for table `activities`
DROP TABLE IF EXISTS `activities`;
CREATE TABLE `activities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `category_id` bigint(20) unsigned NOT NULL,
  `cover_image` varchar(255) DEFAULT NULL,
  `activity_date` date DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `is_published` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activities_category_id_foreign` (`category_id`),
  CONSTRAINT `activities_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `activity_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `activities`
INSERT INTO `activities` (`id`, `title`, `description`, `category_id`, `cover_image`, `activity_date`, `location`, `is_published`, `created_at`, `updated_at`) VALUES
('8', 'กดเ', 'งานบุญประจำปีที่จัดขึ้นเพื่อสร้างความสามัคคีในชุมชน มีการทำบุญตักบาตร การแสดงพื้นบ้าน และกิจกรรมต่างๆ มากมาย', '1', 'activities/x2zJXaFZTaOnVmPTihOIANP3rvshd1aQ5tgdXMKT.jpg', '2025-06-13', 'วัดพระแก้ว', '1', '2025-07-13 15:44:36', '2025-07-13 16:16:45'),
('9', 'กดเกดเ', 'พิธีสวดอภิธรรมเพื่อส่งกุศลให้แก่ผู้ล่วงลับ จัดขึ้นด้วยความเคารพและศรัทธา', '2', 'activities/mpLkJkZnxsmalKfbVlJZ0ILrNfMc3Zycbr0l3KDJ.jpg', '2025-06-28', 'ศาลาการเปรียญ', '1', '2025-07-13 15:44:36', '2025-07-13 17:01:11');

-- Table structure for table `activity_categories`
DROP TABLE IF EXISTS `activity_categories`;
CREATE TABLE `activity_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `color` varchar(7) NOT NULL DEFAULT '#007bff',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `activity_categories`
INSERT INTO `activity_categories` (`id`, `name`, `description`, `color`, `is_active`, `created_at`, `updated_at`) VALUES
('1', 'งานบุญ', 'กิจกรรมงานบุญต่างๆ ที่จัดขึ้นในชุมชน', '#28a745', '1', '2025-07-13 15:13:08', '2025-07-13 15:13:08'),
('2', 'งานศพ', 'กิจกรรมและพิธีกรรมที่เกี่ยวข้องกับงานศพ', '#6c757d', '1', '2025-07-13 15:13:08', '2025-07-13 15:13:08'),
('7', 'งานศพ', 'กิจกรรมและพิธีกรรมที่เกี่ยวข้องกับงานศพ', '#6c757d', '1', '2025-07-13 15:13:32', '2025-07-13 15:13:32'),
('8', 'กิจกรรมชุมชน', 'กิจกรรมต่างๆ ที่จัดขึ้นเพื่อชุมชน', '#17a2b8', '1', '2025-07-13 15:13:32', '2025-07-13 15:13:32'),
('9', 'งานเทศกาล', 'งานเทศกาลประจำปีและงานพิเศษต่างๆ', '#ffc107', '1', '2025-07-13 15:13:32', '2025-07-13 15:13:32'),
('10', 'งานพิเศษ', 'งานพิเศษและกิจกรรมเฉพาะกิจ', '#dc3545', '1', '2025-07-13 15:13:32', '2025-07-13 15:13:32'),
('11', 'งานบุญ', 'กิจกรรมงานบุญต่างๆ ที่จัดขึ้นในชุมชน', '#28a745', '1', '2025-07-13 15:33:46', '2025-07-13 15:33:46'),
('12', 'งานศพ', 'กิจกรรมและพิธีกรรมที่เกี่ยวข้องกับงานศพ', '#6c757d', '1', '2025-07-13 15:33:46', '2025-07-13 15:33:46'),
('13', 'กิจกรรมชุมชน', 'กิจกรรมต่างๆ ที่จัดขึ้นเพื่อชุมชน', '#17a2b8', '1', '2025-07-13 15:33:46', '2025-07-13 15:33:46'),
('14', 'งานเทศกาล', 'งานเทศกาลประจำปีและงานพิเศษต่างๆ', '#ffc107', '1', '2025-07-13 15:33:46', '2025-07-13 15:33:46'),
('15', 'งานพิเศษ', 'งานพิเศษและกิจกรรมเฉพาะกิจ', '#dc3545', '1', '2025-07-13 15:33:46', '2025-07-13 15:33:46'),
('16', 'งานบุญ', 'กิจกรรมงานบุญต่างๆ ที่จัดขึ้นในชุมชน', '#28a745', '1', '2025-07-13 15:44:30', '2025-07-13 15:44:30'),
('17', 'งานศพ', 'กิจกรรมและพิธีกรรมที่เกี่ยวข้องกับงานศพ', '#6c757d', '1', '2025-07-13 15:44:30', '2025-07-13 15:44:30'),
('18', 'กิจกรรมชุมชน', 'กิจกรรมต่างๆ ที่จัดขึ้นเพื่อชุมชน', '#17a2b8', '1', '2025-07-13 15:44:30', '2025-07-13 15:44:30'),
('19', 'งานเทศกาล', 'งานเทศกาลประจำปีและงานพิเศษต่างๆ', '#ffc107', '1', '2025-07-13 15:44:30', '2025-07-13 15:44:30');

-- Table structure for table `activity_images`
DROP TABLE IF EXISTS `activity_images`;
CREATE TABLE `activity_images` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) unsigned NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `caption` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activity_images_activity_id_foreign` (`activity_id`),
  CONSTRAINT `activity_images_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `activity_images`
INSERT INTO `activity_images` (`id`, `activity_id`, `image_path`, `caption`, `sort_order`, `created_at`, `updated_at`) VALUES
('16', '8', 'activities/gallery/8_1.jpg', 'ภาพกิจกรรม งานบุญประจำปี 2567 ภาพที่ 1', '0', '2025-07-13 15:44:36', '2025-07-13 15:44:36'),
('17', '8', 'activities/gallery/8_2.jpg', 'ภาพกิจกรรม งานบุญประจำปี 2567 ภาพที่ 2', '1', '2025-07-13 15:44:36', '2025-07-13 15:44:36'),
('18', '8', 'activities/gallery/8_3.jpg', 'ภาพกิจกรรม งานบุญประจำปี 2567 ภาพที่ 3', '2', '2025-07-13 15:44:36', '2025-07-13 15:44:36'),
('19', '9', 'activities/gallery/9_1.jpg', 'ภาพกิจกรรม พิธีสวดอภิธรรม ภาพที่ 1', '0', '2025-07-13 15:44:36', '2025-07-13 15:44:36'),
('20', '9', 'activities/gallery/9_2.jpg', 'ภาพกิจกรรม พิธีสวดอภิธรรม ภาพที่ 2', '1', '2025-07-13 15:44:36', '2025-07-13 15:44:36'),
('21', '9', 'activities/gallery/9_3.jpg', 'ภาพกิจกรรม พิธีสวดอภิธรรม ภาพที่ 3', '2', '2025-07-13 15:44:36', '2025-07-13 15:44:36'),
('31', '8', 'activities/gallery/7JeqGIjkxmqjgzlGTAs0l1bILF34bCroCyXyGNvV.png', NULL, '3', '2025-07-13 15:54:15', '2025-07-13 15:54:15'),
('32', '9', 'activities/gallery/4lUSmyed6cIKdWb0Zgaj5RxR5o9wN2udYYXWc7Hy.png', NULL, '3', '2025-07-13 16:10:55', '2025-07-13 16:10:55'),
('33', '9', 'activities/gallery/Mk8zXI3HbpWgvYycKjHlbTDUhxYuU69lxbpLaYIy.png', NULL, '4', '2025-07-13 17:00:57', '2025-07-13 17:00:57');

-- Table structure for table `contacts`
DROP TABLE IF EXISTS `contacts`;
CREATE TABLE `contacts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `message` text DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `contacts`
INSERT INTO `contacts` (`id`, `name`, `phone`, `email`, `address`, `message`, `is_read`, `created_at`, `updated_at`) VALUES
('1', 'ผู้ใหญ่จากบริการ', '************', '<EMAIL>', '123 หมู่ 4 ต.ตัวอย่าง อ.เมือง จ.กรุงเทพฯ', 'ติดต่อสอบถามบริการได้ตลอด 24 ชั่วโมง', '1', '2025-07-13 15:13:08', '2025-07-13 15:23:24');

-- Table structure for table `failed_jobs`
DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `homepage_contents`
DROP TABLE IF EXISTS `homepage_contents`;
CREATE TABLE `homepage_contents` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `section` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `content` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `button_text` varchar(255) DEFAULT NULL,
  `button_link` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `homepage_contents`
INSERT INTO `homepage_contents` (`id`, `section`, `title`, `content`, `image`, `button_text`, `button_link`, `created_at`, `updated_at`) VALUES
('1', 'hero', 'ยินดีต้อนรับสู่ SoloShop', 'แพลตฟอร์มการขายออนไลน์ที่ครบครันสำหรับธุรกิจของคุณ พร้อมระบบจัดการที่ใช้งานง่าย', NULL, 'เริ่มต้นใช้งาน', '/services', '2025-07-13 15:13:08', '2025-07-13 15:13:08'),
('2', 'about', 'เกี่ยวกับเรา', 'เราคือทีมงานมืออาชีพด้านการพัฒนาระบบขายออนไลน์ พร้อมให้บริการและสนับสนุนธุรกิจของคุณให้เติบโตอย่างยั่งยืน', NULL, 'ติดต่อเรา', '/contact', '2025-07-13 15:13:08', '2025-07-13 15:13:08'),
('3', 'features', 'คุณสมบัติเด่น', 'ระบบขายออนไลน์ที่ครบครัน รองรับการชำระเงินหลากหลายช่องทาง พร้อมระบบจัดการสินค้าและคำสั่งซื้อที่ทันสมัย', NULL, 'ดูแพ็คเกจ', '/packages', '2025-07-13 15:13:08', '2025-07-13 15:13:08');

-- Table structure for table `migrations`
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `migrations`
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
('1', '2014_10_12_000000_create_users_table', '1'),
('2', '2014_10_12_100000_create_password_resets_table', '1'),
('3', '2019_08_19_000000_create_failed_jobs_table', '1'),
('4', '2019_12_14_000001_create_personal_access_tokens_table', '1'),
('5', '2025_07_12_162611_create_services_table', '1'),
('6', '2025_07_12_163000_create_packages_table', '1'),
('7', '2025_07_12_163639_create_homepage_contents_table', '1'),
('8', '2025_07_12_164012_create_contacts_table', '1'),
('9', '2025_07_12_165000_create_site_settings_table', '1'),
('10', '2025_07_12_190743_add_is_admin_to_users_table', '1'),
('11', '2025_07_13_140000_create_activity_categories_table', '1'),
('12', '2025_07_13_140100_create_activities_table', '1'),
('13', '2025_07_13_140200_create_activity_images_table', '1');

-- Table structure for table `packages`
DROP TABLE IF EXISTS `packages`;
CREATE TABLE `packages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `packages`
INSERT INTO `packages` (`id`, `name`, `description`, `price`, `image`, `created_at`, `updated_at`) VALUES
('1', 'แพ็กเกจประหยัด', 'เหมาะสำหรับงานเล็ก งบประมาณประหยัด', '25000.00', NULL, '2025-07-13 15:13:08', '2025-07-13 15:13:08'),
('2', 'แพ็กเกจพรีเมียม', 'ครบทุกบริการ พร้อมดอกไม้และอาหารระดับพรีเมียม', '90000.00', NULL, '2025-07-13 15:13:08', '2025-07-13 15:13:08');

-- Table structure for table `password_resets`
DROP TABLE IF EXISTS `password_resets`;
CREATE TABLE `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `personal_access_tokens`
DROP TABLE IF EXISTS `personal_access_tokens`;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `services`
DROP TABLE IF EXISTS `services`;
CREATE TABLE `services` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `services`
INSERT INTO `services` (`id`, `title`, `description`, `image`, `price`, `created_at`, `updated_at`) VALUES
('1', 'บริการจัดงานศพครบวงจร', 'บริการจัดงานศพแบบครบวงจร ทั้งพิธีทางศาสนา ดอกไม้ อาหาร และอุปกรณ์', NULL, '50000.00', '2025-07-13 15:13:08', '2025-07-13 15:13:08'),
('2', 'บริการรถรับส่งศพ', 'บริการรถรับส่งศพพร้อมพนักงานดูแล', NULL, '8000.00', '2025-07-13 15:13:08', '2025-07-13 15:13:08'),
('3', 'บริการจัดดอกไม้', 'บริการจัดดอกไม้สำหรับงานศพ พวงหรีด พวงมาลา', NULL, '3000.00', '2025-07-13 15:13:08', '2025-07-13 15:13:08'),
('4', 'บริการจัดอาหาร', 'บริการจัดอาหารสำหรับงานศพ อาหารคาว หวาน เครื่องดื่ม', NULL, '15000.00', '2025-07-13 15:13:08', '2025-07-13 15:13:08');

-- Table structure for table `site_settings`
DROP TABLE IF EXISTS `site_settings`;
CREATE TABLE `site_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `site_name` varchar(255) NOT NULL DEFAULT 'SoloShop',
  `site_description` text DEFAULT NULL,
  `site_logo` varchar(255) DEFAULT NULL,
  `site_favicon` varchar(255) DEFAULT NULL,
  `hero_icon` varchar(255) DEFAULT NULL,
  `hero_title` text DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `contact_phone` varchar(255) DEFAULT NULL,
  `contact_address` text DEFAULT NULL,
  `facebook_url` varchar(255) DEFAULT NULL,
  `twitter_url` varchar(255) DEFAULT NULL,
  `instagram_url` varchar(255) DEFAULT NULL,
  `line_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `site_settings`
INSERT INTO `site_settings` (`id`, `site_name`, `site_description`, `site_logo`, `site_favicon`, `hero_icon`, `hero_title`, `contact_email`, `contact_phone`, `contact_address`, `facebook_url`, `twitter_url`, `instagram_url`, `line_url`, `created_at`, `updated_at`) VALUES
('1', 'SoloShop', 'ผู้เชี่ยวชาญด้านการพัฒนาเว็บไซต์และการตลาดดิจิทัล', NULL, 'settings/tihjiFus3gB28plfmQQUs2M39Fdv0J2qWHuVB32X.jpg', NULL, 'ยินดีต้อนรับสู่ SoloShop', '<EMAIL>', '02-123-4567', '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110', 'https://facebook.com/soloshop', 'https://twitter.com/soloshop', 'https://instagram.com/soloshop', 'https://line.me/ti/p/@soloshop', '2025-07-13 15:13:08', '2025-07-13 15:37:50');

-- Table structure for table `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT 0,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `users`
INSERT INTO `users` (`id`, `name`, `email`, `is_admin`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
('1', 'Admin SoloShop', '<EMAIL>', '1', '2025-07-13 15:13:08', '$2y$10$DyCN/wXpaHl/DvMGwY6q8Okrkr5JPp2L9oAw3DwRk7raWDpZEdlQW', NULL, '2025-07-13 15:13:08', '2025-07-13 15:13:08');

SET FOREIGN_KEY_CHECKS = 1;
