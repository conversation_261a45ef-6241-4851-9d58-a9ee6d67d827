# 🗄️ SoloShop Database Export

## 📋 ข้อมูลทั่วไป

- **ชื่อฐานข้อมูล**: `soloshop`
- **วันที่ Export**: 2025-07-13 19:16:00
- **ไฟล์**: `soloshop_database.sql`
- **ขนาด**: 277 บรรทัด
- **Character Set**: utf8mb4_unicode_ci

## 📊 โครงสร้างฐานข้อมูล

### ตารางหลัก (13 ตาราง):

1. **activities** - ข้อมูลกิจกรรม
2. **activity_categories** - หมวดหมู่กิจกรรม
3. **activity_images** - รูปภาพในแกลอรี่กิจกรรม
4. **contacts** - ข้อมูลการติดต่อ
5. **failed_jobs** - งานที่ล้มเหลว (Lara<PERSON> Queue)
6. **homepage_contents** - เนื้อหาหน้าแรก
7. **migrations** - ประวัติ Migration
8. **packages** - แพ็คเกจบริการ
9. **password_resets** - รีเซ็ตรหัสผ่าน
10. **personal_access_tokens** - Token API
11. **services** - บริการ
12. **site_settings** - การตั้งค่าเว็บไซต์
13. **users** - ผู้ใช้งาน

## 🔧 วิธีการ Import ฐานข้อมูล

### สำหรับ XAMPP/phpMyAdmin:

1. **เปิด phpMyAdmin**
   ```
   URL: http://localhost/phpmyadmin
   ```

2. **สร้างฐานข้อมูลใหม่**
   - คลิก "New" หรือ "สร้างใหม่"
   - ชื่อฐานข้อมูล: `soloshop`
   - Collation: `utf8mb4_unicode_ci`
   - คลิก "Create"

3. **Import ไฟล์ SQL**
   - เลือกฐานข้อมูล `soloshop`
   - คลิกแท็บ "Import"
   - คลิก "Choose File" และเลือกไฟล์ `soloshop_database.sql`
   - คลิก "Go" หรือ "นำเข้า"

### สำหรับ Command Line:

```bash
# สร้างฐานข้อมูล
mysql -u root -p -e "CREATE DATABASE soloshop CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Import ข้อมูล
mysql -u root -p soloshop < soloshop_database.sql
```

## 📋 ข้อมูลตัวอย่างที่มีอยู่

### 👤 ผู้ใช้งาน (users):
- **Admin**: <EMAIL> / admin123
- **สิทธิ์**: Admin เต็มรูปแบบ

### 🎯 กิจกรรม (activities):
- **กิจกรรม 1**: "กดเ" (งานบุญ)
- **กิจกรรม 2**: "กดเกดเ" (งานศพ)
- รวมรูปภาพในแกลอรี่ 9 รูป

### 📂 หมวดหมู่กิจกรรม (activity_categories):
- งานบุญ (สีเขียว #28a745)
- งานศพ (สีเทา #6c757d)
- กิจกรรมชุมชน (สีฟ้า #17a2b8)
- งานเทศกาล (สีเหลือง #ffc107)
- งานพิเศษ (สีแดง #dc3545)

### 🛠️ บริการ (services):
1. บริการจัดงานศพครบวงจร (50,000 บาท)
2. บริการรถรับส่งศพ (8,000 บาท)
3. บริการจัดดอกไม้ (3,000 บาท)
4. บริการจัดอาหาร (15,000 บาท)

### 📞 ข้อมูลติดต่อ (contacts):
- ผู้ใหญ่จากบริการ
- โทร: 081-234-5678
- อีเมล: <EMAIL>

### ⚙️ การตั้งค่าเว็บไซต์ (site_settings):
- ชื่อเว็บไซต์: SoloShop
- อีเมล: <EMAIL>
- โทร: 02-123-4567
- ที่อยู่: 123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110

## 🔗 ความสัมพันธ์ของตาราง

### Foreign Keys:
```sql
activities.category_id → activity_categories.id
activity_images.activity_id → activities.id
```

### ข้อมูลสำคัญ:
- ตาราง `activities` เชื่อมโยงกับ `activity_categories`
- ตาราง `activity_images` เชื่อมโยงกับ `activities`
- การลบ `activities` จะลบ `activity_images` ที่เกี่ยวข้องด้วย (CASCADE)

## 📁 ไฟล์รูปภาพที่เกี่ยวข้อง

### โครงสร้างโฟลเดอร์:
```
storage/app/public/
├── activities/
│   ├── [cover images]
│   └── gallery/
│       ├── 8_1.jpg, 8_2.jpg, 8_3.jpg
│       ├── 9_1.jpg, 9_2.jpg, 9_3.jpg
│       └── [uploaded images]
└── settings/
    └── tihjiFus3gB28plfmQQUs2M39Fdv0J2qWHuVB32X.jpg
```

## ⚠️ ข้อควรระวัง

1. **รูปภาพ**: ไฟล์รูปภาพไม่ได้รวมอยู่ใน SQL dump
2. **Storage Link**: ต้องสร้าง symbolic link หลัง import
   ```bash
   php artisan storage:link
   ```
3. **Environment**: ตรวจสอบไฟล์ `.env` ให้ตรงกับการตั้งค่าฐานข้อมูล
4. **Permissions**: ตรวจสอบสิทธิ์การเขียนไฟล์ในโฟลเดอร์ `storage`

## 🚀 การใช้งานหลัง Import

### 1. ตั้งค่า Laravel:
```bash
# Copy environment file
cp .env.example .env

# Update database settings in .env
DB_DATABASE=soloshop
DB_USERNAME=root
DB_PASSWORD=

# Generate application key
php artisan key:generate

# Create storage link
php artisan storage:link

# Clear cache
php artisan config:clear
php artisan cache:clear
```

### 2. เข้าสู่ระบบ Admin:
```
URL: http://localhost/SoloShop/public/admin
Email: <EMAIL>
Password: admin123
```

### 3. ทดสอบฟังก์ชัน:
- จัดการกิจกรรม
- จัดการแกลอรี่รูปภาพ
- จัดการบริการ
- จัดการแพ็คเกจ
- ตั้งค่าเว็บไซต์

## 📞 การสนับสนุน

หากพบปัญหาในการ Import หรือใช้งาน:
1. ตรวจสอบ Laravel log: `storage/logs/laravel.log`
2. ตรวจสอบ MySQL error log
3. ตรวจสอบ PHP error log
4. ตรวจสอบการตั้งค่า `.env`

## 📝 หมายเหตุ

- ฐานข้อมูลนี้สร้างขึ้นสำหรับระบบ SoloShop
- มีข้อมูลตัวอย่างครบถ้วนสำหรับการทดสอบ
- รองรับระบบ Activities พร้อมแกลอรี่รูปภาพ
- พร้อมใช้งานทันทีหลัง Import
